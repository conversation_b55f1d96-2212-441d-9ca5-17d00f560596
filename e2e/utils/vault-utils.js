const { exec } = require('child_process');
const path = require('path');

const VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test');
const PRISTINE_VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test.pristine');

/**
 * Restore the test vault from pristine state
 * This ensures each test starts with a clean vault
 * Uses a safer approach that doesn't delete the entire vault while Obsidian is running
 */
function restoreVaultFromPristine() {
    return new Promise((resolve, reject) => {
        console.log('🔄 Restoring vault from pristine state...');

        // Clear articles directory and restore settings and files
        const commands = [
            `rm -f "${VAULT_PATH}/articles/"*.md`,
            `cp "${PRISTINE_VAULT_PATH}/.obsidian/plugins/ghost-sync/data.json" "${VAULT_PATH}/.obsidian/plugins/ghost-sync/data.json"`,
            `cp -r "${PRISTINE_VAULT_PATH}/articles/"* "${VAULT_PATH}/articles/" 2>/dev/null || true`
        ];

        exec(commands.join(' && '), (error) => {
            if (error) {
                console.error('❌ Vault restoration failed:', error);
                reject(error);
            } else {
                console.log('✅ Vault restored from pristine state');
                resolve();
            }
        });
    });
}

/**
 * Copy plugin files to the test vault
 */
function copyPluginFiles() {
    return new Promise((resolve, reject) => {
        console.log('📁 Copying plugin files to test vault...');
        exec('cp main.js manifest.json styles.css tests/vault/Test/.obsidian/plugins/ghost-sync/', (error) => {
            if (error) {
                console.error('❌ Copy failed:', error);
                reject(error);
            } else {
                console.log('✅ Plugin files copied');
                resolve();
            }
        });
    });
}

/**
 * Full vault reset - restore from pristine and copy plugin files
 */
async function resetVault() {
    await restoreVaultFromPristine();
    await copyPluginFiles();
}

module.exports = {
    restoreVaultFromPristine,
    copyPluginFiles,
    resetVault
};
